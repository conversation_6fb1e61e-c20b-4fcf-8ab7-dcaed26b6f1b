import { NextRequest, NextResponse } from 'next/server'
import { createClient, SupabaseClient } from '@supabase/supabase-js'
import crypto from 'crypto'

// Helper function to verify admin status
async function verifyAdmin(supabase: SupabaseClient, userId: string): Promise<boolean> {
  // First try to use the database function we created
  try {
    const { data: funcResult, error: funcError } = await supabase
      .rpc('verify_admin_status', { user_id: userId })

    if (!funcError && funcResult !== null) {
      return funcResult
    }
  } catch (err) {
    console.log('Error using verify_admin_status function, falling back to direct query:', err)
  }

  // Fallback to direct query on user_roles table
  const { data, error } = await supabase
    .from('user_roles')
    .select('role')
    .eq('user_id', userId)
    .single()

  if (error || !data) {
    console.error('Error checking admin status:', error)
    return false
  }

  return data.role === 'admin' || data.role === 'owner'
}

// Helper function to get user ID from authorization header
async function getUserIdFromAuth(request: NextRequest): Promise<string | null> {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.error('No valid Authorization header found')
      return null
    }

    // Extract the token
    const token = authHeader.split(' ')[1]

    if (!token) {
      console.error('No token in Authorization header')
      return null
    }

    // Initialize Supabase admin client to verify the token
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL as string
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY as string

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase configuration')
      return null
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Verify the token and get user information
    const { data, error } = await supabase.auth.getUser(token)

    if (error || !data.user) {
      console.error('Error verifying token:', error)
      return null
    }

    return data.user.id
  } catch (err) {
    console.error('Error getting user ID from token:', err)
    return null
  }
}

// GET: Retrieve client credentials for a company
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL as string
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY as string

    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get user ID from authorization header
    const userId = await getUserIdFromAuth(request)

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized: Invalid or missing authentication token' },
        { status: 401 }
      )
    }

    // Verify admin status
    const isAdmin = await verifyAdmin(supabase, userId)

    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      )
    }

    // Retrieve client credentials
    const { data, error } = await supabase
      .from('client_users')
      .select('*')
      .eq('company_id', companyId)

    if (error) {
      return NextResponse.json(
        { error: 'Failed to retrieve client credentials' },
        { status: 500 }
      )
    }

    return NextResponse.json({ credentials: data || [] })
  } catch (err) {
    console.error('Error retrieving client credentials:', err)
    return NextResponse.json(
      { error: 'Failed to retrieve client credentials: ' + (err instanceof Error ? err.message : 'Unknown error') },
      { status: 500 }
    )
  }
}

// POST: Create or update client credentials
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { companyId, email, password: userPassword } = body

    if (!companyId || !email) {
      return NextResponse.json(
        { error: 'Company ID and email are required' },
        { status: 400 }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL as string
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY as string

    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get user ID from authorization header
    const userId = await getUserIdFromAuth(request)

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized: Invalid or missing authentication token' },
        { status: 401 }
      )
    }

    // Verify admin status
    const isAdmin = await verifyAdmin(supabase, userId)

    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      )
    }

    // Use provided password or generate a secure one if not provided
    const password = userPassword || crypto.randomBytes(8).toString('hex')
    const hashedPassword = await bcryptHash(password)

    // Check if the user already exists
    const { data: existingUser, error: checkError } = await supabase
      .from('client_users')
      .select('id')
      .eq('company_id', companyId)
      .eq('email', email)
      .maybeSingle()

    if (checkError) {
      return NextResponse.json(
        { error: 'Failed to check existing credentials' },
        { status: 500 }
      )
    }

    let result

    // Create or update the user
    if (existingUser) {
      // Update existing user
      const { data, error: updateError } = await supabase
        .from('client_users')
        .update({
          email,
          password_hash: hashedPassword, // Changed from 'password' to 'password_hash' to match table schema
          updated_at: new Date().toISOString(),
          is_active: true
        })
        .eq('id', existingUser.id)
        .select()

      if (updateError) {
        return NextResponse.json(
          { error: 'Failed to update client credentials' },
          { status: 500 }
        )
      }

      result = data
    } else {
      // Create new user
      const { data, error: insertError } = await supabase
        .from('client_users')
        .insert({
          company_id: companyId,
          email,
          password_hash: hashedPassword, // Changed from 'password' to 'password_hash' to match table schema
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          is_active: true
        })
        .select()

      if (insertError) {
        return NextResponse.json(
          { error: 'Failed to create client credentials' },
          { status: 500 }
        )
      }

      result = data
    }

    return NextResponse.json({
      success: true,
      message: 'Client credentials created/updated successfully',
      credentials: {
        ...result?.[0],
        clearTextPassword: password
      }
    })
  } catch (err) {
    console.error('Error creating/updating client credentials:', err)
    return NextResponse.json(
      { error: 'Failed to manage client credentials: ' + (err instanceof Error ? err.message : 'Unknown error') },
      { status: 500 }
    )
  }
}

// PATCH: Update client user status (activate/deactivate)
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, isActive } = body

    if (!id || isActive === undefined) {
      return NextResponse.json(
        { error: 'User ID and status are required' },
        { status: 400 }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL as string
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY as string

    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get user ID from authorization header
    const userId = await getUserIdFromAuth(request)

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized: Invalid or missing authentication token' },
        { status: 401 }
      )
    }

    // Verify admin status
    const isAdmin = await verifyAdmin(supabase, userId)

    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      )
    }

    // Update user status
    const { data, error } = await supabase
      .from('client_users')
      .update({
        is_active: isActive,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()

    if (error) {
      return NextResponse.json(
        { error: 'Failed to update client user status' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Client user status updated successfully',
      user: data?.[0]
    })
  } catch (err) {
    console.error('Error updating client user status:', err)
    return NextResponse.json(
      { error: 'Failed to update client user status: ' + (err instanceof Error ? err.message : 'Unknown error') },
      { status: 500 }
    )
  }
}

// PUT: Reset client password
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id } = body

    if (!id) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL as string
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY as string

    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get user ID from authorization header
    const userId = await getUserIdFromAuth(request)

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized: Invalid or missing authentication token' },
        { status: 401 }
      )
    }

    // Verify admin status
    const isAdmin = await verifyAdmin(supabase, userId)

    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      )
    }

    // Generate a new password
    const password = crypto.randomBytes(8).toString('hex')
    const hashedPassword = await bcryptHash(password)

    // Update the password
    const { data, error } = await supabase
      .from('client_users')
      .update({
        password_hash: hashedPassword, // Changed from 'password' to 'password_hash' to match table schema
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()

    if (error) {
      return NextResponse.json(
        { error: 'Failed to reset client password' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Client password reset successfully',
      user: {
        ...data?.[0],
        clearTextPassword: password
      }
    })
  } catch (err) {
    console.error('Error resetting client password:', err)
    return NextResponse.json(
      { error: 'Failed to reset client password: ' + (err instanceof Error ? err.message : 'Unknown error') },
      { status: 500 }
    )
  }
}

// Helper function for bcrypt hashing
async function bcryptHash(password: string): Promise<string> {
  // Simple hashing for demonstration; in production, use proper bcrypt
  return crypto.createHash('sha256').update(password).digest('hex')
}