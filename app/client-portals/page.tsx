'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { 
  Plus, Search, Building, 
  Users, Briefcase, FileText, MoreHorizontal,
  Filter, Trash2, Edit, Eye, ArrowUpRight,
  Key, UserCog, Database, AlertCircle, CheckCircle, Loader2
} from 'lucide-react'
import { createBrowserClient } from '@supabase/ssr'
import { cn, getStatusBadgeClass } from '../../lib/utils'
import ClientCredentialModal from '../components/ClientCredentialModal'

interface Company {
  id: string
  name: string
  industry: string | null
  status: string | null
  _count?: {
    contacts: number
    projects: number
    credentials: number
    invoices: number
  }
}

interface MigrationStatus {
  clientUsersExists: boolean
  clientSessionsExists: boolean
  verifyFunctionExists: boolean
}

export default function ClientPortals() {
  const [companies, setCompanies] = useState<Company[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [dropdownOpen, setDropdownOpen] = useState<string | null>(null)
  const [credentialModalOpen, setCredentialModalOpen] = useState(false)
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null)
  const [migrationStatus, setMigrationStatus] = useState<MigrationStatus | null>(null)
  const [checkingMigration, setCheckingMigration] = useState(false)
  const [runningMigration, setRunningMigration] = useState(false)
  const [migrationMessage, setMigrationMessage] = useState<{ type: 'success' | 'error', message: string } | null>(null)
  
  // Check if client authentication is set up
  const checkMigrationStatus = async () => {
    setCheckingMigration(true)
    setMigrationMessage(null)
    
    try {
      // Get the current Supabase session
      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      )
      
      const { data: sessionData } = await supabase.auth.getSession()
      const authToken = sessionData?.session?.access_token
      
      const response = await fetch('/api/migrations/client-portal-auth', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })
      
      if (!response.ok) {
        throw new Error('Failed to check migration status')
      }
      
      const data = await response.json()
      setMigrationStatus(data)
    } catch (err) {
      console.error('Error checking migration status:', err)
      setMigrationMessage({
        type: 'error',
        message: 'Failed to check migration status'
      })
    } finally {
      setCheckingMigration(false)
    }
  }
  
  // Run the migrations
  const runMigrations = async () => {
    setRunningMigration(true)
    setMigrationMessage(null)
    
    try {
      // Get the current Supabase session
      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      )
      
      const { data: sessionData } = await supabase.auth.getSession()
      const authToken = sessionData?.session?.access_token
      
      const response = await fetch('/api/migrations/client-portal-auth', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })
      
      if (!response.ok) {
        throw new Error('Failed to run migrations')
      }
      
      const data = await response.json()
      
      if (data.success) {
        setMigrationMessage({
          type: 'success',
          message: 'Client authentication system set up successfully'
        })
        
        // Check migration status again
        checkMigrationStatus()
      } else {
        throw new Error(data.error || 'Unknown error')
      }
    } catch (err) {
      console.error('Error running migrations:', err)
      setMigrationMessage({
        type: 'error',
        message: err instanceof Error ? err.message : 'Failed to run migrations'
      })
    } finally {
      setRunningMigration(false)
    }
  }

  useEffect(() => {
    async function fetchCompanies() {
      setIsLoading(true)

      try {
        // Create a Supabase client
        const supabase = createBrowserClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
        )

        const { data, error } = await supabase
          .from('companies')
          .select('id, name, industry, status')
          .order('name')

        if (error) {
          console.error('Error fetching companies:', error)
          setCompanies([])
        } else if (data) {
          // Fetch counts for each company
          const companiesWithCounts = await Promise.all(
            data.map(async (company) => {
              // Get contacts count
              const { count: contactsCount } = await supabase
                .from('contacts')
                .select('id', { count: 'exact', head: true })
                .eq('company_id', company.id)

              // Get projects count
              const { count: projectsCount } = await supabase
                .from('projects')
                .select('id', { count: 'exact', head: true })
                .eq('company_id', company.id)

              // Get credentials count
              const { count: credentialsCount } = await supabase
                .from('credentials')
                .select('id', { count: 'exact', head: true })
                .eq('company_id', company.id)

              // Get invoices count
              const { count: invoicesCount } = await supabase
                .from('invoices')
                .select('id', { count: 'exact', head: true })
                .eq('company_id', company.id)

              return {
                ...company,
                _count: {
                  contacts: contactsCount || 0,
                  projects: projectsCount || 0,
                  credentials: credentialsCount || 0,
                  invoices: invoicesCount || 0
                }
              }
            })
          )

          setCompanies(companiesWithCounts)
        }
      } catch (err) {
        console.error('Unexpected error:', err)
        setCompanies([])
      }

      setIsLoading(false)
    }

    fetchCompanies()
    checkMigrationStatus()
  }, [])

  // Filter companies based on search query
  const filteredCompanies = companies.filter(company => {
    return company.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      company.industry?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      company.status?.toLowerCase().includes(searchQuery.toLowerCase())
  })

  // Toggle dropdown menu
  const toggleDropdown = (id: string) => {
    if (dropdownOpen === id) {
      setDropdownOpen(null)
    } else {
      setDropdownOpen(id)
    }
  }

  // Open credential management modal
  const openCredentialModal = (company: Company) => {
    setSelectedCompany(company)
    setCredentialModalOpen(true)
    setDropdownOpen(null) // Close any open dropdown
  }

  // Handle click outside dropdown
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (dropdownOpen && !(e.target as Element).closest('.dropdown-menu')) {
        setDropdownOpen(null)
      }
    }

    document.addEventListener('click', handleClickOutside)
    return () => document.removeEventListener('click', handleClickOutside)
  }, [dropdownOpen])

  // Check if client auth is fully set up
  const isClientAuthSetup = migrationStatus?.clientUsersExists && 
    migrationStatus?.clientSessionsExists && 
    migrationStatus?.verifyFunctionExists;

  return (
    <div className="animate-fade-in py-6 space-y-8 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">Client Portals</h1>
          <p className="text-muted-foreground mt-1">
            Manage client portals and access client-specific information
          </p>
        </div>

        <div className="flex gap-3">
          {!isClientAuthSetup && (
            <button 
              onClick={runMigrations}
              disabled={runningMigration || checkingMigration}
              className="btn btn-outline flex items-center gap-1.5 px-4 py-2"
            >
              {runningMigration ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Setting up...
                </>
              ) : (
                <>
                  <Database className="h-4 w-4" />
                  Setup Auth System
                </>
              )}
            </button>
          )}
          <Link
            href="/companies/new"
            className="btn btn-primary flex items-center gap-1.5 px-4 py-2"
          >
            <Plus size={16} />
            New Client
          </Link>
        </div>
      </div>

      {/* Migration status message */}
      {migrationMessage && (
        <div className={cn(
          "p-4 rounded-md flex items-start",
          migrationMessage.type === 'success' 
            ? "bg-success/10 border border-success/20 text-success" 
            : "bg-destructive/10 border border-destructive/20 text-destructive"
        )}>
          {migrationMessage.type === 'success' 
            ? <CheckCircle className="h-5 w-5 mr-2 mt-0.5" /> 
            : <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />}
          <div>{migrationMessage.message}</div>
        </div>
      )}

      {/* Auth status */}
      {!isClientAuthSetup && !migrationMessage && (
        <div className="bg-amber-50 border border-amber-200 text-amber-800 p-4 rounded-md flex items-start">
          <Database className="h-5 w-5 mr-2 mt-0.5" />
          <div>
            <p className="font-medium">Client portal authentication system not fully set up</p>
            <p className="mt-1 text-sm">
              You need to set up the client authentication system before you can create client portal users. 
              Click the &quot;Setup Auth System&quot; button to create the necessary database tables.
            </p>
          </div>
        </div>
      )}

      {/* Search and filter bar */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search */}
        <div className="relative flex-grow">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-muted-foreground" />
          </div>
          <input
            type="text"
            className="block w-full pl-10 pr-3 py-2 border border-border rounded-lg bg-card placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 focus:border-transparent text-sm transition-all"
            placeholder="Search clients..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {/* Filter button */}
        <button className="btn btn-outline flex items-center gap-2 px-4 py-2">
          <Filter size={16} />
          <span>Filter</span>
        </button>
      </div>

      {/* Client portals grid */}
      {isLoading ? (
        <div className="flex justify-center items-center h-64" data-testid="loading-indicator">
           <div className="w-16 h-16 relative">
             <div className="absolute top-0 left-0 right-0 bottom-0 rounded-full border-4 border-primary/20"></div>
             <div className="absolute top-0 left-0 right-0 bottom-0 rounded-full border-4 border-transparent border-t-primary animate-spin"></div>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCompanies.length > 0 ? (
            filteredCompanies.map((company) => (
              <div key={company.id} className="card group relative">
                {/* Dropdown menu trigger */}
                <div className="absolute top-4 right-4 z-10">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleDropdown(company.id);
                    }}
                    className="p-1.5 rounded-md hover:bg-muted text-muted-foreground focus:outline-none transition-colors"
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </button>

                  {/* Dropdown menu */}
                  {dropdownOpen === company.id && (
                    <div className="absolute right-0 mt-1 w-48 bg-card rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 py-1 dropdown-menu border border-border">
                      <button
                        onClick={() => openCredentialModal(company)}
                        className="flex w-full items-center px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors"
                        disabled={!isClientAuthSetup}
                      >
                        <UserCog className="mr-2 h-4 w-4 text-muted-foreground" />
                        Manage Access
                      </button>
                      <Link
                        href={`/companies/${company.id}/edit`}
                        className="flex items-center px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors"
                      >
                        <Edit className="mr-2 h-4 w-4 text-muted-foreground" />
                        Edit
                      </Link>
                      <Link
                        href={`/companies/${company.id}`}
                        className="flex items-center px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors"
                      >
                        <Eye className="mr-2 h-4 w-4 text-muted-foreground" />
                        View in CRM
                      </Link>
                      <button
                        className="flex w-full items-center px-4 py-2 text-sm text-destructive hover:bg-destructive/10 transition-colors"
                        onClick={(e) => {
                          e.stopPropagation();
                          // Handle delete logic
                          alert(`Delete ${company.name}`);
                        }}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </button>
                    </div>
                  )}
                </div>

                <div className="p-6 h-full">
                  {/* Client Info */}
                  <div className="flex items-start space-x-4">
                    <div className="bg-primary/10 p-3 rounded-xl">
                      <Building className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h2 className="text-xl font-medium group-hover:text-primary transition-colors mb-1">
                        {company.name}
                      </h2>
                      <div className="flex flex-wrap gap-2 items-center text-sm">
                        {company.industry && (
                          <span className="text-muted-foreground">
                            {company.industry}
                          </span>
                        )}
                        {company.status && (
                          <span className={cn("badge", getStatusBadgeClass(company.status))}>
                            {company.status}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Metrics */}
                  <div className="mt-6 grid grid-cols-2 gap-3">
                    <div className="rounded-lg bg-muted/50 p-3">
                      <div className="flex items-center gap-2">
                        <Briefcase className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">{company._count?.projects || 0}</span>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">Projects</p>
                    </div>
                    <div className="rounded-lg bg-muted/50 p-3">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">{company._count?.contacts || 0}</span>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">Contacts</p>
                    </div>
                    <div className="rounded-lg bg-muted/50 p-3">
                      <div className="flex items-center gap-2">
                        <Key className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">{company._count?.credentials || 0}</span>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">Credentials</p>
                    </div>
                    <div className="rounded-lg bg-muted/50 p-3">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">{company._count?.invoices || 0}</span>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">Invoices</p>
                    </div>
                  </div>

                  {/* Action buttons */}
                  <div className="mt-6 flex gap-3">
                    <button
                      onClick={() => openCredentialModal(company)}
                      className={cn(
                        "btn btn-outline flex-1 gap-1.5 transition-all",
                        !isClientAuthSetup && "opacity-50 cursor-not-allowed"
                      )}
                      disabled={!isClientAuthSetup}
                    >
                      <UserCog className="h-4 w-4" />
                      Manage Access
                    </button>
                    <Link 
                      href={`/client-view/${company.id}`} 
                      className="btn btn-outline flex-1 gap-1.5 group-hover:border-primary group-hover:text-primary transition-all"
                    >
                      Access Portal
                      <ArrowUpRight className="h-4 w-4" />
                    </Link>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="col-span-1 md:col-span-2 lg:col-span-3 card p-6 flex flex-col items-center justify-center text-center">
              <div className="bg-muted rounded-full p-3 mb-4">
                <Search className="h-6 w-6 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-1">No clients found</h3>
              <p className="text-muted-foreground mb-4">
                We couldn&apos;t find any clients matching your search criteria.
              </p>
              <button
                onClick={() => setSearchQuery('')}
                className="btn btn-outline"
              >
                Clear Search
              </button>
            </div>
          )}
        </div>
      )}

      {/* Client Credential Modal */}
      {selectedCompany && (
        <ClientCredentialModal
          isOpen={credentialModalOpen}
          onClose={() => setCredentialModalOpen(false)}
          companyId={selectedCompany.id}
          companyName={selectedCompany.name}
        />
      )}
    </div>
  )
}
