'use client'

import { useState, useEffect, useCallback } from 'react'
import { Eye, EyeOff, User, Key as KeyI<PERSON>, Check, AlertCircle, Loader2 } from 'lucide-react'
import { cn } from '../../lib/utils'
import { createBrowserClient } from '@supabase/ssr'
import { Credential } from '../../lib/supabase/types'; // Import shared Credential type
import { Button } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog"

interface ClientUser {
  id: string
  email: string
  is_active: boolean
  created_at: string
  last_login: string | null
}

interface ClientCredentialModalProps {
  isOpen: boolean
  onClose: () => void
  companyId?: string
  companyName?: string
  credential?: Credential | null
}

export default function ClientCredentialModal({
  isOpen,
  onClose,
  companyId,
  companyName,
  credential
}: ClientCredentialModalProps) {
  const [users, setUsers] = useState<ClientUser[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const [activeTab, setActiveTab] = useState<'users' | 'new'>('users')
  const [newUserEmail, setNewUserEmail] = useState('')
  const [newUserPassword, setNewUserPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formError, setFormError] = useState<string | null>(null)
  const [formSuccess, setFormSuccess] = useState<string | null>(null)

  const fetchUsers = useCallback(async () => {
    if (!companyId) {
      if (credential) {
        setIsLoading(false);
        return;
      }
      setError("Company ID is missing, cannot fetch users.");
      setIsLoading(false);
      return;
    }
    setIsLoading(true)
    setError(null)

    try {
      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      )
      const { data: sessionData } = await supabase.auth.getSession()
      const authToken = sessionData?.session?.access_token

      const response = await fetch(`/api/client-auth/manage?companyId=${companyId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      })
      if (!response.ok) throw new Error('Failed to fetch client users')
      const data = await response.json()
      setUsers(data.users || [])
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'Failed to load client users')
    } finally {
      setIsLoading(false)
    }
  }, [companyId, credential])

  const handleCreateUser = async (e: React.FormEvent) => {
    e.preventDefault()
    setFormError(null)
    setFormSuccess(null)
    setIsSubmitting(true)
    try {
      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      )
      const { data: sessionData } = await supabase.auth.getSession()
      const authToken = sessionData?.session?.access_token
      const response = await fetch('/api/client-auth/manage', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${authToken}` },
        body: JSON.stringify({ email: newUserEmail, password: newUserPassword, companyId })
      })
      const data = await response.json()
      if (!response.ok) throw new Error(data.error || 'Failed to create client user')
      setNewUserEmail('')
      setNewUserPassword('')
      setFormSuccess('Client user created successfully')
      fetchUsers()
      setTimeout(() => { setActiveTab('users'); setFormSuccess(null) }, 2000)
    } catch (err: unknown) {
      setFormError(err instanceof Error ? err.message : 'Failed to create client user')
    } finally {
      setIsSubmitting(false)
    }
  }

  const toggleUserStatus = async (userId: string, currentStatus: boolean) => {
    try {
      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      )
      const { data: sessionData } = await supabase.auth.getSession()
      const authToken = sessionData?.session?.access_token
      const response = await fetch('/api/client-auth/manage', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${authToken}` },
        body: JSON.stringify({ id: userId, isActive: !currentStatus })
      })
      if (!response.ok) throw new Error('Failed to update user status')
      setUsers(users.map(user => user.id === userId ? { ...user, is_active: !currentStatus } : user))
    } catch (err: unknown) {
      console.error('Error updating user status:', err)
      setError('Failed to update user status. Please try again.')
    }
  }

  const resetUserPassword = async (userId: string, userEmail: string) => {
    try {
      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      )
      const { data: sessionData } = await supabase.auth.getSession()
      const authToken = sessionData?.session?.access_token
      const response = await fetch('/api/client-auth/manage', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${authToken}` },
        body: JSON.stringify({ id: userId })
      })
      if (!response.ok) throw new Error('Failed to reset password')
      const data = await response.json()
      const newPassword = data.user?.clearTextPassword || 'Password reset successfully'
      setFormSuccess(`Password reset for ${userEmail}. New password: ${newPassword}`)
      setTimeout(() => setFormSuccess(null), 10000)
    } catch (err: unknown) {
      console.error('Error resetting password:', err)
      setError('Failed to reset password. Please try again.')
    }
  }

  const deleteUser = async (userId: string, userEmail: string) => {
    if (!confirm(`Are you sure you want to delete ${userEmail}? This action cannot be undone.`)) return
    try {
      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      )
      const { data: sessionData } = await supabase.auth.getSession()
      const authToken = sessionData?.session?.access_token
      const response = await fetch(`/api/client-auth/manage?userId=${userId}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${authToken}` }
      })
      if (!response.ok) throw new Error('Failed to delete user')
      setUsers(users.filter(user => user.id !== userId))
      setFormSuccess(`User ${userEmail} deleted successfully`)
      setTimeout(() => setFormSuccess(null), 3000)
    } catch (err: unknown) {
      console.error('Error deleting user:', err)
      setError('Failed to delete user. Please try again.')
    }
  }

  useEffect(() => {
    if (isOpen && companyId && !credential) {
      fetchUsers()
    } else if (isOpen && credential) {
      setIsLoading(false);
      setError(null);
    }
  }, [isOpen, companyId, fetchUsers, credential])

  useEffect(() => {
    if (!isOpen) {
      setActiveTab('users')
      setNewUserEmail('')
      setNewUserPassword('')
      setFormError(null)
      setFormSuccess(null)
    }
  }, [isOpen])

  console.log('ClientCredentialModal rendering with:', { isOpen, credential });

  // Use a simpler approach to avoid potential issues with the Dialog component
  if (!isOpen) return null;

  return (
    <Dialog open={true} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[525px]">
        {credential ? (
          <>
            <DialogHeader>
              <DialogTitle>{credential.name || 'Credential Details'}</DialogTitle>
              <DialogDescription>
                Type: {credential.credential_type || 'N/A'}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-3 py-4">
              {credential.username && (
                <div>
                  <p className="text-xs font-medium text-muted-foreground">USERNAME</p>
                  <p className="text-sm">{credential.username}</p>
                </div>
              )}
              {credential.password_decrypted ? (
                <div>
                  <p className="text-xs font-medium text-muted-foreground">PASSWORD</p>
                  <p className="text-sm font-mono bg-slate-100 dark:bg-slate-800 px-2 py-1 rounded break-all">{credential.password_decrypted}</p>
                </div>
              ) : (
                <div>
                  <p className="text-xs font-medium text-muted-foreground">PASSWORD</p>
                  <p className="text-sm text-muted-foreground italic">Password not available</p>
                </div>
              )}
              {credential.url && (
                <div>
                  <p className="text-xs font-medium text-muted-foreground">URL</p>
                  <a
                    href={credential.url.startsWith('http://') || credential.url.startsWith('https://') ? credential.url : `https://${credential.url}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:underline block truncate"
                  >
                    {credential.url}
                  </a>
                </div>
              )}
              {credential.notes && (
                <div>
                  <p className="text-xs font-medium text-muted-foreground">NOTES</p>
                  <p className="text-sm whitespace-pre-wrap bg-slate-50 dark:bg-slate-800/50 p-2 rounded border border-slate-200 dark:border-slate-700">{credential.notes}</p>
                </div>
              )}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={onClose}>Close</Button>
            </DialogFooter>
          </>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle>Manage Client Portal Users for {companyName || 'this company'}</DialogTitle>
              <DialogDescription>
                Here you can view existing client users and create new ones.
              </DialogDescription>
            </DialogHeader>

            <div className="flex border-b border-border">
              <button
                className={cn(
                  "px-4 py-3 font-medium text-sm transition-colors",
                  activeTab === 'users'
                    ? "border-b-2 border-primary text-primary"
                    : "text-muted-foreground hover:text-foreground"
                )}
                onClick={() => setActiveTab('users')}
              >
                Existing Users
              </button>
              <button
                className={cn(
                  "px-4 py-3 font-medium text-sm transition-colors",
                  activeTab === 'new'
                    ? "border-b-2 border-primary text-primary"
                    : "text-muted-foreground hover:text-foreground"
                )}
                onClick={() => setActiveTab('new')}
              >
                Create New User
              </button>
            </div>

            <div className="p-5 overflow-y-auto flex-grow">
              {error && (
                <div className="mb-4 bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 rounded-md flex items-start">
                  <AlertCircle className="h-5 w-5 mr-2 mt-0.5 shrink-0" />
                  <div>{error}</div>
                </div>
              )}
              {formSuccess && !formError && (
                <div className="mb-4 bg-green-100 border border-green-200 text-green-700 px-4 py-3 rounded-md flex items-start">
                  <Check className="h-5 w-5 mr-2 mt-0.5 shrink-0" />
                  <div>{formSuccess}</div>
                </div>
              )}

              {activeTab === 'users' && (
                <div>
                  <h3 className="text-md font-medium mb-4">Manage Portal Users</h3>
                  {isLoading ? (
                    <div className="py-8 flex justify-center">
                      <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                    </div>
                  ) : users.length === 0 ? (
                    <div className="text-center py-6 bg-muted/30 rounded-lg">
                      <User className="h-10 w-10 mx-auto text-muted-foreground mb-2" />
                      <p className="text-muted-foreground">No client users found</p>
                      <button
                        className="mt-3 text-primary font-medium"
                        onClick={() => setActiveTab('new')}
                      >
                        Create the first user
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {users.map(user => (
                        <div
                          key={user.id}
                          className="border border-border rounded-lg p-4 flex flex-col sm:flex-row justify-between gap-4"
                        >
                          <div className="flex-grow">
                            <h4 className="font-medium">{user.email}</h4>
                            <p className="text-sm text-muted-foreground">Client User</p>
                            <div className="mt-1 flex items-center">
                              <span className={cn(
                                "inline-flex items-center gap-1 px-2 py-0.5 text-xs rounded-full",
                                user.is_active
                                  ? "bg-green-100 text-green-700"
                                  : "bg-red-100 text-red-700"
                              )}>
                                {user.is_active ? 'Active' : 'Inactive'}
                              </span>
                              {user.last_login && (
                                <span className="text-xs text-muted-foreground ml-2">
                                  Last login: {new Date(user.last_login).toLocaleDateString()}
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="flex flex-wrap gap-2 items-center justify-end">
                            <Button
                              variant={user.is_active ? "destructive" : "default"}
                              size="sm"
                              onClick={() => toggleUserStatus(user.id, user.is_active)}
                            >
                              {user.is_active ? 'Deactivate' : 'Activate'}
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => resetUserPassword(user.id, user.email)}
                            >
                              Reset Password
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-destructive/50 text-destructive hover:bg-destructive/10"
                              onClick={() => deleteUser(user.id, user.email)}
                            >
                              Delete
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'new' && (
                <div>
                  <h3 className="text-md font-medium mb-4">Create New Portal User</h3>
                  <form onSubmit={handleCreateUser} className="space-y-4">
                    {formError && (
                      <div className="bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 rounded-md flex items-start">
                        <AlertCircle className="h-5 w-5 mr-2 mt-0.5 shrink-0" />
                        <div>{formError}</div>
                      </div>
                    )}

                    <div>
                      <label htmlFor="email" className="block text-sm font-medium mb-1 text-muted-foreground">Email Address</label>
                      <input
                        id="email" type="email" value={newUserEmail} onChange={(e) => setNewUserEmail(e.target.value)}
                        className="w-full rounded-md border border-border bg-background px-3 py-2 text-sm focus:ring-2 focus:ring-primary/50 focus:border-primary"
                        placeholder="<EMAIL>" required
                      />
                    </div>
                    <div>
                      <label htmlFor="password" className="block text-sm font-medium mb-1 text-muted-foreground">Password</label>
                      <div className="relative">
                        <input
                          id="password" type={showPassword ? 'text' : 'password'} value={newUserPassword} onChange={(e) => setNewUserPassword(e.target.value)}
                          className="w-full rounded-md border border-border bg-background px-3 py-2 text-sm focus:ring-2 focus:ring-primary/50 focus:border-primary pr-10"
                          placeholder="Enter secure password" required minLength={8}
                        />
                        <button
                          type="button"
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                        </button>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">Password must be at least 8 characters</p>
                    </div>
                    <div className="flex justify-end pt-2">
                      <Button variant="outline" type="button" onClick={() => setActiveTab('users')} className="mr-2" disabled={isSubmitting}>
                        Cancel
                      </Button>
                      <Button type="submit" disabled={isSubmitting}>
                        {isSubmitting ? (
                          <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Creating...</>
                        ) : (
                          <><KeyIcon className="mr-2 h-4 w-4" /> Create User</>
                        )}
                      </Button>
                    </div>
                  </form>
                </div>
              )}
            </div>
            <DialogFooter className="p-4 border-t border-border">
               <Button variant="outline" onClick={onClose}>Close</Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  )
}